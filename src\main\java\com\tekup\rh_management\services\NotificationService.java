package com.tekup.rh_management.services;

import com.tekup.rh_management.dto.NotificationMessage;
import com.tekup.rh_management.entities.Conges;
import com.tekup.rh_management.entities.User;
import com.tekup.rh_management.repositories.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class NotificationService {
    
    private final SimpMessagingTemplate messagingTemplate;
    private final UserRepository userRepository;
    
    /**
     * Notifie la création d'une nouvelle demande de congé
     */
    public void notifyCongeCreated(Conges conge) {
        log.info("Envoi notification création congé ID: {}", conge.getId());
        
        // Notification à l'employé
        NotificationMessage employeeNotification = NotificationMessage.builder()
                .type(NotificationMessage.TYPE_CONGE_CREATED)
                .title("Demande de congé créée")
                .message("Votre demande de congé du " + conge.getDateDebut() + " au " + conge.getDateFin() + " a été créée et est en attente de validation.")
                .congeId(conge.getId())
                .employeeName(conge.getEmployee().getUsername())
                .status(conge.getStatus().getDescription())
                .timestamp(LocalDateTime.now())
                .build();
        
        sendNotificationToUser(conge.getEmployee().getUsername(), employeeNotification);
        
        // Notification aux managers
        notifyManagers(conge, "Nouvelle demande de congé", 
                "Une nouvelle demande de congé de " + conge.getEmployee().getUsername() + 
                " est en attente de votre validation.");
    }
    
    /**
     * Notifie la validation par le manager
     */
    public void notifyManagerValidation(Conges conge, boolean approved) {
        log.info("Envoi notification validation manager pour congé ID: {}, approuvé: {}", conge.getId(), approved);
        
        String type = approved ? NotificationMessage.TYPE_CONGE_VALIDATED_BY_MANAGER : 
                                NotificationMessage.TYPE_CONGE_REJECTED_BY_MANAGER;
        String title = approved ? "Demande approuvée par le manager" : "Demande rejetée par le manager";
        String message = approved ? 
                "Votre demande de congé a été approuvée par votre manager et est maintenant en attente de validation RH." :
                "Votre demande de congé a été rejetée par votre manager.";
        
        // Notification à l'employé
        NotificationMessage employeeNotification = NotificationMessage.builder()
                .type(type)
                .title(title)
                .message(message)
                .congeId(conge.getId())
                .employeeName(conge.getEmployee().getUsername())
                .status(conge.getStatus().getDescription())
                .timestamp(LocalDateTime.now())
                .build();
        
        sendNotificationToUser(conge.getEmployee().getUsername(), employeeNotification);
        
        // Si approuvé, notifier les RH
        if (approved) {
            notifyRH(conge, "Demande en attente de validation RH", 
                    "Une demande de congé de " + conge.getEmployee().getUsername() + 
                    " approuvée par le manager est en attente de votre validation finale.");
        }
    }
    
    /**
     * Notifie la validation finale par les RH
     */
    public void notifyRHValidation(Conges conge, boolean approved) {
        log.info("Envoi notification validation RH pour congé ID: {}, approuvé: {}", conge.getId(), approved);
        
        String type = approved ? NotificationMessage.TYPE_CONGE_APPROVED_BY_RH : 
                                NotificationMessage.TYPE_CONGE_REJECTED_BY_RH;
        String title = approved ? "Demande approuvée définitivement" : "Demande rejetée par les RH";
        String message = approved ? 
                "Votre demande de congé a été approuvée définitivement par les RH." :
                "Votre demande de congé a été rejetée par les RH.";
        
        // Notification à l'employé
        NotificationMessage employeeNotification = NotificationMessage.builder()
                .type(type)
                .title(title)
                .message(message)
                .congeId(conge.getId())
                .employeeName(conge.getEmployee().getUsername())
                .status(conge.getStatus().getDescription())
                .timestamp(LocalDateTime.now())
                .build();
        
        sendNotificationToUser(conge.getEmployee().getUsername(), employeeNotification);
        
        // Notification au manager qui a validé
        if (conge.getManager() != null) {
            NotificationMessage managerNotification = NotificationMessage.builder()
                    .type(type)
                    .title("Décision RH sur demande de congé")
                    .message("La demande de congé de " + conge.getEmployee().getUsername() + 
                            " que vous aviez approuvée a été " + (approved ? "approuvée" : "rejetée") + " par les RH.")
                    .congeId(conge.getId())
                    .employeeName(conge.getEmployee().getUsername())
                    .status(conge.getStatus().getDescription())
                    .timestamp(LocalDateTime.now())
                    .build();
            
            sendNotificationToUser(conge.getManager().getUsername(), managerNotification);
        }
    }
    
    /**
     * Envoie une notification à un utilisateur spécifique
     */
    private void sendNotificationToUser(String username, NotificationMessage notification) {
        try {
            messagingTemplate.convertAndSendToUser(username, "/queue/notifications", notification);
            log.debug("Notification envoyée à l'utilisateur: {}", username);
        } catch (Exception e) {
            log.error("Erreur lors de l'envoi de notification à l'utilisateur {}: {}", username, e.getMessage());
        }
    }
    
    /**
     * Notifie tous les managers
     */
    private void notifyManagers(Conges conge, String title, String message) {
        List<User> managers = userRepository.findByRoles_Name("MANAGER");
        
        for (User manager : managers) {
            NotificationMessage notification = NotificationMessage.builder()
                    .type(NotificationMessage.TYPE_CONGE_CREATED)
                    .title(title)
                    .message(message)
                    .congeId(conge.getId())
                    .employeeName(conge.getEmployee().getUsername())
                    .status(conge.getStatus().getDescription())
                    .timestamp(LocalDateTime.now())
                    .build();
            
            sendNotificationToUser(manager.getUsername(), notification);
        }
    }
    
    /**
     * Notifie tous les RH
     */
    private void notifyRH(Conges conge, String title, String message) {
        List<User> rhUsers = userRepository.findByRoles_Name("RH");
        
        for (User rh : rhUsers) {
            NotificationMessage notification = NotificationMessage.builder()
                    .type(NotificationMessage.TYPE_CONGE_VALIDATED_BY_MANAGER)
                    .title(title)
                    .message(message)
                    .congeId(conge.getId())
                    .employeeName(conge.getEmployee().getUsername())
                    .status(conge.getStatus().getDescription())
                    .timestamp(LocalDateTime.now())
                    .build();
            
            sendNotificationToUser(rh.getUsername(), notification);
        }
    }
}
