package com.tekup.rh_management.services;

import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tekup.rh_management.dto.AuthenticationRequest;
import com.tekup.rh_management.dto.AuthenticationResponse;
import com.tekup.rh_management.dto.RegisterRequest;
import com.tekup.rh_management.dto.RegisterResponse;
import com.tekup.rh_management.dto.UsernameCheckResponse;
import com.tekup.rh_management.entities.Role;
import com.tekup.rh_management.entities.User;
import com.tekup.rh_management.exceptions.AuthenticationException;
import com.tekup.rh_management.repositories.RoleRepository;
import com.tekup.rh_management.repositories.UserRepository;
import com.tekup.rh_management.security.JwtService;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
@Transactional
public class AuthenticationServiceImpl implements AuthenticationService {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtService jwtService;
    private final AuthenticationManager authenticationManager;

    @Override
    public RegisterResponse register(RegisterRequest request) {
        // Check if user already exists
        if (userRepository.findByUsername(request.getUsername()).isPresent()) {
            throw new AuthenticationException("Username already exists");
        }
        if (userRepository.findByEmail(request.getEmail()).isPresent()) {
            throw new AuthenticationException("Email already exists");
        }

        // Create user
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));

        // Set role (single role)
        Role userRole;
        if (request.getRoles() != null && !request.getRoles().isEmpty()) {
            String roleName = request.getRoles().iterator().next(); // Take first role only
            userRole = roleRepository.findByName(roleName)
                    .orElseGet(() -> {
                        Role newRole = new Role();
                        newRole.setName(roleName);
                        return roleRepository.save(newRole);
                    });
        } else {
            // Default role if none specified
            userRole = roleRepository.findByName("EMPLOYEE")
                    .orElseGet(() -> {
                        Role newRole = new Role();
                        newRole.setName("EMPLOYEE");
                        return roleRepository.save(newRole);
                    });
        }
        user.setRole(userRole);

        userRepository.save(user);

        return RegisterResponse.builder()
                .message("User registered successfully")
                .build();
    }

    @Override
    public UsernameCheckResponse checkUsernameAvailability(String username) {
        boolean isAvailable = !userRepository.findByUsername(username).isPresent();
        
        return UsernameCheckResponse.builder()
                .username(username)
                .available(isAvailable)
                .message(isAvailable ? "Username is available" : "Username already exists")
                .build();
    }

    @Override
    public AuthenticationResponse authenticate(AuthenticationRequest request) {
        authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        request.getUsername(),
                        request.getPassword()
                )
        );

        User user = userRepository.findByUsernameWithRole(request.getUsername())
                .orElseThrow(() -> new AuthenticationException("User not found"));

        String jwtToken = jwtService.generateToken(user);
        String refreshToken = jwtService.generateRefreshToken(user);

        return AuthenticationResponse.builder()
                .token(jwtToken)
                .refreshToken(refreshToken)
                .username(user.getUsername())
                .email(user.getEmail())
                .roles(user.getRole() != null ?
                        java.util.Set.of(user.getRole().getName()) :
                        java.util.Set.of())
                .build();
    }
} 