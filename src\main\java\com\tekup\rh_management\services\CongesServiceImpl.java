package com.tekup.rh_management.services;

import com.tekup.rh_management.dto.CongeRequest;
import com.tekup.rh_management.dto.CongeResponse;
import com.tekup.rh_management.dto.CongeValidationRequest;
import com.tekup.rh_management.entities.Conges;
import com.tekup.rh_management.entities.User;
import com.tekup.rh_management.enums.CongeStatus;
import com.tekup.rh_management.exceptions.ResourceNotFoundException;
import com.tekup.rh_management.repositories.CongesRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CongesServiceImpl implements ICongesService {
    
    private final CongesRepository congesRepository;
    private final NotificationService notificationService;
    
    @Override
    public CongeResponse createCongeRequest(CongeRequest request, User employee) {
        log.info("Création d'une demande de congé pour l'employé: {}", employee.getUsername());
        
        // Vérifier les conflits de dates
        List<Conges> conflictingConges = congesRepository.findConflictingConges(
                employee, request.getDateDebut(), request.getDateFin());
        
        if (!conflictingConges.isEmpty()) {
            throw new IllegalArgumentException("Il existe déjà des congés approuvés ou en cours de validation pour cette période");
        }
        
        // Créer la demande de congé
        Conges conge = new Conges();
        conge.setType(request.getType());
        conge.setNombreJours(request.getNombreJours());
        conge.setDateDebut(request.getDateDebut());
        conge.setDateFin(request.getDateFin());
        conge.setMotif(request.getMotif());
        conge.setEmployee(employee);
        conge.setStatus(CongeStatus.PENDING);
        
        Conges savedConge = congesRepository.save(conge);
        
        // Envoyer notification
        notificationService.notifyCongeCreated(savedConge);
        
        return convertToResponse(savedConge);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CongeResponse> getCongesByEmployee(User employee) {
        List<Conges> conges = congesRepository.findByEmployeeOrderByCreatedAtDesc(employee);
        return conges.stream().map(this::convertToResponse).collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CongeResponse> getPendingCongesForManager() {
        List<Conges> conges = congesRepository.findPendingForManagerValidation();
        return conges.stream().map(this::convertToResponse).collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CongeResponse> getPendingCongesForRH() {
        List<Conges> conges = congesRepository.findPendingForRHValidation();
        return conges.stream().map(this::convertToResponse).collect(Collectors.toList());
    }
    
    @Override
    public CongeResponse validateByManager(Long congeId, CongeValidationRequest validation, User manager) {
        log.info("Validation par manager {} de la demande de congé ID: {}", manager.getUsername(), congeId);
        
        Conges conge = congesRepository.findByIdWithDetails(congeId)
                .orElseThrow(() -> new ResourceNotFoundException("Demande de congé non trouvée"));
        
        if (!conge.getStatus().canBeValidatedByManager()) {
            throw new IllegalStateException("Cette demande ne peut pas être validée par un manager dans son état actuel");
        }
        
        // Mettre à jour le statut
        if (validation.getApproved()) {
            conge.setStatus(CongeStatus.ACCEPTED_BY_MANAGER);
        } else {
            conge.setStatus(CongeStatus.REJECTED_BY_MANAGER);
        }
        
        conge.setManager(manager);
        conge.setManagerValidatedAt(LocalDateTime.now());
        conge.setManagerComment(validation.getComment());
        
        Conges savedConge = congesRepository.save(conge);
        
        // Envoyer notification
        notificationService.notifyManagerValidation(savedConge, validation.getApproved());
        
        return convertToResponse(savedConge);
    }
    
    @Override
    public CongeResponse validateByRH(Long congeId, CongeValidationRequest validation, User rh) {
        log.info("Validation par RH {} de la demande de congé ID: {}", rh.getUsername(), congeId);
        
        Conges conge = congesRepository.findByIdWithDetails(congeId)
                .orElseThrow(() -> new ResourceNotFoundException("Demande de congé non trouvée"));
        
        if (!conge.getStatus().canBeValidatedByRH()) {
            throw new IllegalStateException("Cette demande ne peut pas être validée par les RH dans son état actuel");
        }
        
        // Mettre à jour le statut
        if (validation.getApproved()) {
            conge.setStatus(CongeStatus.APPROVED_BY_RH);
        } else {
            conge.setStatus(CongeStatus.REJECTED_BY_RH);
        }
        
        conge.setRh(rh);
        conge.setRhValidatedAt(LocalDateTime.now());
        conge.setRhComment(validation.getComment());
        
        Conges savedConge = congesRepository.save(conge);
        
        // Envoyer notification
        notificationService.notifyRHValidation(savedConge, validation.getApproved());
        
        return convertToResponse(savedConge);
    }
    
    @Override
    @Transactional(readOnly = true)
    public CongeResponse getCongeById(Long id) {
        Conges conge = congesRepository.findByIdWithDetails(id)
                .orElseThrow(() -> new ResourceNotFoundException("Demande de congé non trouvée"));
        return convertToResponse(conge);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CongeResponse> getAllConges() {
        List<Conges> conges = congesRepository.findAll();
        return conges.stream().map(this::convertToResponse).collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CongeResponse> getCongesValidatedByManager(User manager) {
        List<Conges> conges = congesRepository.findByManagerOrderByManagerValidatedAtDesc(manager);
        return conges.stream().map(this::convertToResponse).collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CongeResponse> getCongesValidatedByRH(User rh) {
        List<Conges> conges = congesRepository.findByRhOrderByRhValidatedAtDesc(rh);
        return conges.stream().map(this::convertToResponse).collect(Collectors.toList());
    }
    
    /**
     * Convertit une entité Conges en CongeResponse
     */
    private CongeResponse convertToResponse(Conges conge) {
        CongeResponse.CongeResponseBuilder builder = CongeResponse.builder()
                .id(conge.getId())
                .type(conge.getType())
                .nombreJours(conge.getNombreJours())
                .dateDebut(conge.getDateDebut())
                .dateFin(conge.getDateFin())
                .status(conge.getStatus())
                .motif(conge.getMotif())
                .createdAt(conge.getCreatedAt())
                .statusDescription(conge.getStatus().getDescription());
        
        // Informations sur l'employé
        if (conge.getEmployee() != null) {
            builder.employeeId(conge.getEmployee().getId())
                   .employeeName(conge.getEmployee().getUsername())
                   .employeeEmail(conge.getEmployee().getEmail());
        }
        
        // Informations sur le manager
        if (conge.getManager() != null) {
            builder.managerId(conge.getManager().getId())
                   .managerName(conge.getManager().getUsername())
                   .managerValidatedAt(conge.getManagerValidatedAt())
                   .managerComment(conge.getManagerComment());
        }
        
        // Informations sur les RH
        if (conge.getRh() != null) {
            builder.rhId(conge.getRh().getId())
                   .rhName(conge.getRh().getUsername())
                   .rhValidatedAt(conge.getRhValidatedAt())
                   .rhComment(conge.getRhComment());
        }
        
        return builder.build();
    }
}
