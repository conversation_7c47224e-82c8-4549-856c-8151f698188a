package com.tekup.rh_management.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NotificationMessage {
    
    private String type;
    private String title;
    private String message;
    private Long congeId;
    private String employeeName;
    private String status;
    private LocalDateTime timestamp;
    
    // Types de notifications
    public static final String TYPE_CONGE_CREATED = "CONGE_CREATED";
    public static final String TYPE_CONGE_VALIDATED_BY_MANAGER = "CONGE_VALIDATED_BY_MANAGER";
    public static final String TYPE_CONGE_REJECTED_BY_MANAGER = "CONGE_REJECTED_BY_MANAGER";
    public static final String TYPE_CONGE_APPROVED_BY_RH = "CONGE_APPROVED_BY_RH";
    public static final String TYPE_CONGE_REJECTED_BY_RH = "CONGE_REJECTED_BY_RH";
}
