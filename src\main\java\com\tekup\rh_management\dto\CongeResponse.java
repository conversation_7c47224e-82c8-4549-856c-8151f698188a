package com.tekup.rh_management.dto;

import com.tekup.rh_management.enums.CongeStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CongeResponse {
    
    private Long id;
    private String type;
    private int nombreJours;
    private LocalDate dateDebut;
    private LocalDate dateFin;
    private CongeStatus status;
    private String motif;
    
    // Informations sur l'employé
    private Long employeeId;
    private String employeeName;
    private String employeeEmail;
    
    // Informations sur le manager
    private Long managerId;
    private String managerName;
    private LocalDateTime managerValidatedAt;
    private String managerComment;
    
    // Informations sur les RH
    private Long rhId;
    private String rhName;
    private LocalDateTime rhValidatedAt;
    private String rhComment;
    
    // Dates de création et modification
    private LocalDateTime createdAt;
    
    // Statut descriptif
    private String statusDescription;
}
