-- Insertion des rôles de base si ils n'existent pas déjà
INSERT IGNORE INTO roles (name) VALUES ('EMPLOYEE');
INSERT IGNORE INTO roles (name) VALUES ('MANAGER');
INSERT IGNORE INTO roles (name) VALUES ('RH');

-- Exemple d'utilisateurs de test (mot de passe: "password" encodé en BCrypt)
-- Vous pouvez décommenter ces lignes pour créer des utilisateurs de test
/*
-- Créer les utilisateurs avec leurs rôles directement
INSERT IGNORE INTO users (username, email, password, role_id)
SELECT 'employee1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', r.id
FROM roles r WHERE r.name = 'EMPLOYEE';

INSERT IGNORE INTO users (username, email, password, role_id)
SELECT 'manager1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', r.id
FROM roles r WHERE r.name = 'MANAGER';

INSERT IGNORE INTO users (username, email, password, role_id)
SELECT 'rh1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', r.id
FROM roles r WHERE r.name = 'RH';
*/
