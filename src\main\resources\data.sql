-- Insertion des rôles de base si ils n'existent pas déjà
INSERT IGNORE INTO roles (name) VALUES ('EMPLOYEE');
INSERT IGNORE INTO roles (name) VALUES ('MANAGER');
INSERT IGNORE INTO roles (name) VALUES ('RH');

-- Exemple d'utilisateurs de test (mot de passe: "password" encodé en BCrypt)
-- Vous pouvez décommenter ces lignes pour créer des utilisateurs de test
/*
INSERT IGNORE INTO users (username, email, password) VALUES 
('employee1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.'),
('manager1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.'),
('rh1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.');

-- Attribution des rôles aux utilisateurs de test
INSERT IGNORE INTO user_roles (user_id, role_id) 
SELECT u.id, r.id FROM users u, roles r 
WHERE u.username = 'employee1' AND r.name = 'EMPLOYEE';

INSERT IGNORE INTO user_roles (user_id, role_id) 
SELECT u.id, r.id FROM users u, roles r 
WHERE u.username = 'manager1' AND r.name = 'MANAGER';

INSERT IGNORE INTO user_roles (user_id, role_id) 
SELECT u.id, r.id FROM users u, roles r 
WHERE u.username = 'rh1' AND r.name = 'RH';
*/
