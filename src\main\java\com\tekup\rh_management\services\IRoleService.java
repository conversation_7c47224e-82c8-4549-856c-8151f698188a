package com.tekup.rh_management.services;
import com.tekup.rh_management.entities.Role;
import com.tekup.rh_management.entities.User;

import java.util.List;

public interface IRoleService {

    public Role createRole(Role role);
    public Role updateRole(Long id, Role updatedRole);
    public void deleteRole(Long id);
    public List<Role> getAllRoles();
    public List<User> getUsersByRoleId(Long roleId);
    public void assignRoleToUser(Long roleId, Long userId);
    public void removeRoleFromUser(Long roleId, Long userId);
}
