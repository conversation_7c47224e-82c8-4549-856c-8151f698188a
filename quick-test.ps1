# Test rapide de l'API
$baseUrl = "http://localhost:9090"

Write-Host "Test de connexion à l'application..." -ForegroundColor Yellow

try {
    # Test simple de connexion
    $response = Invoke-WebRequest -Uri "$baseUrl/swagger-ui.html" -Method GET -TimeoutSec 5
    Write-Host "✓ Application accessible sur le port 9090" -ForegroundColor Green
    
    # Test de création d'utilisateur RH
    Write-Host "Création d'un utilisateur RH..." -ForegroundColor Yellow
    
    $userData = @{
        username = "rh_test"
        email = "<EMAIL>"
        password = "password"
        roles = @("RH")
    } | ConvertTo-Json

    try {
        $createResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/register" -Method POST -ContentType "application/json" -Body $userData
        Write-Host "✓ Utilisateur RH créé: $($createResponse.message)" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Utilisateur existe déjà ou autre erreur" -ForegroundColor Yellow
    }
    
    # Test d'authentification
    Write-Host "Test d'authentification..." -ForegroundColor Yellow
    
    $loginData = @{
        username = "rh_test"
        password = "password"
    } | ConvertTo-Json

    $authResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/authenticate" -Method POST -ContentType "application/json" -Body $loginData
    $token = $authResponse.token
    Write-Host "✓ Authentification réussie" -ForegroundColor Green
    Write-Host "Rôles: $($authResponse.roles -join ', ')" -ForegroundColor Cyan
    
    # Test de l'endpoint debug
    Write-Host "Test de l'endpoint debug..." -ForegroundColor Yellow
    
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }

    $debugInfo = Invoke-RestMethod -Uri "$baseUrl/api/v0/conges/debug/user-info" -Method GET -Headers $headers
    Write-Host "✓ Debug info récupérée:" -ForegroundColor Green
    Write-Host "  Username: $($debugInfo.username)" -ForegroundColor Cyan
    Write-Host "  Role: $($debugInfo.role)" -ForegroundColor Cyan
    Write-Host "  Authorities: $($debugInfo.authorities -join ', ')" -ForegroundColor Cyan
    
    # Test de l'endpoint problématique
    Write-Host "Test de l'endpoint pending-rh..." -ForegroundColor Yellow
    
    try {
        $pendingRH = Invoke-RestMethod -Uri "$baseUrl/api/v0/conges/pending-rh" -Method GET -Headers $headers
        Write-Host "✓ Endpoint pending-rh accessible - $($pendingRH.Count) demandes trouvées" -ForegroundColor Green
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "✗ Erreur $statusCode sur endpoint pending-rh" -ForegroundColor Red
        
        # Lire le détail de l'erreur
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Détail erreur: $errorBody" -ForegroundColor Red
    }
    
} catch {
    Write-Host "✗ Application non accessible: $($_.Exception.Message)" -ForegroundColor Red
}
