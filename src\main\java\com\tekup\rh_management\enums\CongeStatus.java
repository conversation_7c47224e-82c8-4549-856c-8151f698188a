package com.tekup.rh_management.enums;

/**
 * Énumération représentant les différents statuts d'une demande de congé
 */
public enum CongeStatus {
    /**
     * Demande créée par l'employé, en attente de validation du manager
     */
    PENDING("En attente de validation du manager"),
    
    /**
     * Demande acceptée par le manager, en attente de validation RH
     */
    ACCEPTED_BY_MANAGER("Acceptée par le manager, en attente de validation RH"),
    
    /**
     * Demande rejetée par le manager
     */
    REJECTED_BY_MANAGER("Rejetée par le manager"),
    
    /**
     * Demande approuvée définitivement par les RH
     */
    APPROVED_BY_RH("Approuvée par les RH"),
    
    /**
     * Demande rejetée par les RH
     */
    REJECTED_BY_RH("Rejetée par les RH");
    
    private final String description;
    
    CongeStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Vérifie si le statut permet une validation par le manager
     */
    public boolean canBeValidatedByManager() {
        return this == PENDING;
    }
    
    /**
     * Vérifie si le statut permet une validation par les RH
     */
    public boolean canBeValidatedByRH() {
        return this == ACCEPTED_BY_MANAGER;
    }
    
    /**
     * Vérifie si la demande est dans un état final
     */
    public boolean isFinal() {
        return this == REJECTED_BY_MANAGER || 
               this == APPROVED_BY_RH || 
               this == REJECTED_BY_RH;
    }
}
