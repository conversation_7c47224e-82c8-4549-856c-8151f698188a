package com.tekup.rh_management.controllers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.tekup.rh_management.dto.CongeRequest;
import com.tekup.rh_management.dto.CongeResponse;
import com.tekup.rh_management.dto.CongeValidationRequest;
import com.tekup.rh_management.entities.User;
import com.tekup.rh_management.services.ICongesService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/v0/conges")
@RequiredArgsConstructor
@Tag(name = "Congés", description = "API de gestion des demandes de congés")
@SecurityRequirement(name = "bearer authentication")
public class CongesController {
    
    private final ICongesService congesService;
    
    @PostMapping
    @PreAuthorize("hasAuthority('ROLE_EMPLOYEE')")
    @Operation(summary = "Créer une demande de congé",
               description = "Permet à un employé de créer une nouvelle demande de congé")
    @ApiResponse(responseCode = "201", description = "Demande de congé créée avec succès")
    @ApiResponse(responseCode = "400", description = "Données invalides ou conflit de dates")
    @ApiResponse(responseCode = "403", description = "Accès refusé - rôle EMPLOYEE requis")
    public ResponseEntity<CongeResponse> createCongeRequest(
            @Valid @RequestBody CongeRequest request,
            @AuthenticationPrincipal User currentUser) {

        CongeResponse response = congesService.createCongeRequest(request, currentUser);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping("/my-conges")
    @PreAuthorize("hasAuthority('ROLE_EMPLOYEE')")
    @Operation(summary = "Obtenir mes demandes de congé",
               description = "Permet à un employé de voir toutes ses demandes de congé")
    public ResponseEntity<List<CongeResponse>> getMyConges(
            @AuthenticationPrincipal User currentUser) {

        List<CongeResponse> conges = congesService.getCongesByEmployee(currentUser);
        return ResponseEntity.ok(conges);
    }

    @GetMapping("/pending-manager")
    @PreAuthorize("hasAuthority('ROLE_MANAGER')")
    @Operation(summary = "Obtenir les demandes en attente de validation manager",
               description = "Permet à un manager de voir toutes les demandes en attente de sa validation")
    public ResponseEntity<List<CongeResponse>> getPendingCongesForManager() {
        List<CongeResponse> conges = congesService.getPendingCongesForManager();
        return ResponseEntity.ok(conges);
    }

    @GetMapping("/pending-rh")
    @PreAuthorize("hasAuthority('ROLE_RH')")
    @Operation(summary = "Obtenir les demandes en attente de validation RH",
               description = "Permet aux RH de voir toutes les demandes en attente de validation finale")
    public ResponseEntity<List<CongeResponse>> getPendingCongesForRH() {
        List<CongeResponse> conges = congesService.getPendingCongesForRH();
        return ResponseEntity.ok(conges);
    }
    
    @PostMapping("/{congeId}/validate-manager")
    @PreAuthorize("hasAuthority('ROLE_MANAGER')")
    @Operation(summary = "Valider une demande en tant que manager",
               description = "Permet à un manager d'approuver ou rejeter une demande de congé")
    @ApiResponse(responseCode = "200", description = "Demande validée avec succès")
    @ApiResponse(responseCode = "404", description = "Demande de congé non trouvée")
    @ApiResponse(responseCode = "400", description = "Demande ne peut pas être validée dans son état actuel")
    public ResponseEntity<CongeResponse> validateByManager(
            @Parameter(description = "ID de la demande de congé") @PathVariable Long congeId,
            @Valid @RequestBody CongeValidationRequest validation,
            @AuthenticationPrincipal User currentUser) {

        CongeResponse response = congesService.validateByManager(congeId, validation, currentUser);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/{congeId}/validate-rh")
    @PreAuthorize("hasAuthority('ROLE_RH')")
    @Operation(summary = "Valider une demande en tant que RH",
               description = "Permet aux RH d'approuver ou rejeter définitivement une demande de congé")
    @ApiResponse(responseCode = "200", description = "Demande validée avec succès")
    @ApiResponse(responseCode = "404", description = "Demande de congé non trouvée")
    @ApiResponse(responseCode = "400", description = "Demande ne peut pas être validée dans son état actuel")
    public ResponseEntity<CongeResponse> validateByRH(
            @Parameter(description = "ID de la demande de congé") @PathVariable Long congeId,
            @Valid @RequestBody CongeValidationRequest validation,
            @AuthenticationPrincipal User currentUser) {

        CongeResponse response = congesService.validateByRH(congeId, validation, currentUser);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{congeId}")
    @PreAuthorize("hasAuthority('ROLE_EMPLOYEE') or hasAuthority('ROLE_MANAGER') or hasAuthority('ROLE_RH')")
    @Operation(summary = "Obtenir une demande de congé par ID",
               description = "Permet d'obtenir les détails d'une demande de congé spécifique")
    public ResponseEntity<CongeResponse> getCongeById(
            @Parameter(description = "ID de la demande de congé") @PathVariable Long congeId) {

        CongeResponse response = congesService.getCongeById(congeId);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/all")
    @PreAuthorize("hasAuthority('ROLE_RH')")
    @Operation(summary = "Obtenir toutes les demandes de congé",
               description = "Permet aux RH de voir toutes les demandes de congé du système")
    public ResponseEntity<List<CongeResponse>> getAllConges() {
        List<CongeResponse> conges = congesService.getAllConges();
        return ResponseEntity.ok(conges);
    }

    @GetMapping("/validated-by-me-manager")
    @PreAuthorize("hasAuthority('ROLE_MANAGER')")
    @Operation(summary = "Obtenir les demandes que j'ai validées en tant que manager",
               description = "Permet à un manager de voir toutes les demandes qu'il a validées")
    public ResponseEntity<List<CongeResponse>> getCongesValidatedByMe(
            @AuthenticationPrincipal User currentUser) {

        List<CongeResponse> conges = congesService.getCongesValidatedByManager(currentUser);
        return ResponseEntity.ok(conges);
    }

    @GetMapping("/validated-by-me-rh")
    @PreAuthorize("hasAuthority('ROLE_RH')")
    @Operation(summary = "Obtenir les demandes que j'ai validées en tant que RH",
               description = "Permet à un RH de voir toutes les demandes qu'il a validées")
    public ResponseEntity<List<CongeResponse>> getCongesValidatedByMeRH(
            @AuthenticationPrincipal User currentUser) {

        List<CongeResponse> conges = congesService.getCongesValidatedByRH(currentUser);
        return ResponseEntity.ok(conges);
    }

    @GetMapping("/debug/user-info")
    @Operation(summary = "Debug - Informations utilisateur",
               description = "Endpoint de debug pour vérifier les informations de l'utilisateur connecté")
    public ResponseEntity<Map<String, Object>> getUserDebugInfo(
            @AuthenticationPrincipal User currentUser) {

        Map<String, Object> debugInfo = new HashMap<>();
        debugInfo.put("username", currentUser.getUsername());
        debugInfo.put("email", currentUser.getEmail());
        debugInfo.put("role", currentUser.getRole() != null ? currentUser.getRole().getName() : "NO_ROLE");
        debugInfo.put("authorities", currentUser.getAuthorities().stream()
                .map(auth -> auth.getAuthority())
                .collect(java.util.stream.Collectors.toList()));

        return ResponseEntity.ok(debugInfo);
    }
}
