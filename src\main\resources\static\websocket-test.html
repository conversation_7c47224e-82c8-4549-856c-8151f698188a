<!DOCTYPE html>
<html>
<head>
    <title>Test WebSocket - Système de Congés</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .notification { padding: 10px; margin: 5px 0; border-radius: 3px; }
        .notification.success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .notification.info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .notification.warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .notification.error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        input, textarea { padding: 5px; margin: 5px; width: 200px; }
        #notifications { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test WebSocket - Système de Congés RH</h1>
        
        <div class="section">
            <h3>Connexion WebSocket</h3>
            <button id="connect">Se connecter</button>
            <button id="disconnect">Se déconnecter</button>
            <span id="status">Déconnecté</span>
        </div>
        
        <div class="section">
            <h3>Test de notification</h3>
            <input type="text" id="testMessage" placeholder="Message de test" />
            <button id="sendTest">Envoyer test</button>
        </div>
        
        <div class="section">
            <h3>Authentification (pour les tests)</h3>
            <input type="text" id="username" placeholder="Nom d'utilisateur" />
            <input type="password" id="password" placeholder="Mot de passe" />
            <button id="login">Se connecter</button>
            <span id="authStatus">Non authentifié</span>
        </div>
        
        <div class="section">
            <h3>Notifications reçues</h3>
            <div id="notifications"></div>
            <button id="clearNotifications">Effacer</button>
        </div>
    </div>

    <script>
        let stompClient = null;
        let token = null;

        function setConnected(connected) {
            document.getElementById('connect').disabled = connected;
            document.getElementById('disconnect').disabled = !connected;
            document.getElementById('sendTest').disabled = !connected;
            document.getElementById('status').textContent = connected ? 'Connecté' : 'Déconnecté';
        }

        function connect() {
            const socket = new SockJS('/ws');
            stompClient = Stomp.over(socket);
            
            const headers = {};
            if (token) {
                headers['Authorization'] = 'Bearer ' + token;
            }
            
            stompClient.connect(headers, function (frame) {
                setConnected(true);
                console.log('Connected: ' + frame);
                
                // S'abonner aux notifications personnelles
                stompClient.subscribe('/user/queue/notifications', function (notification) {
                    showNotification(JSON.parse(notification.body), 'info');
                });
                
                // S'abonner aux notifications générales
                stompClient.subscribe('/topic/notifications', function (notification) {
                    showNotification(JSON.parse(notification.body), 'warning');
                });
                
                // Envoyer message de connexion
                stompClient.send("/app/connect", {}, JSON.stringify({}));
                
            }, function(error) {
                console.log('Connection error: ' + error);
                setConnected(false);
                showNotification({
                    title: 'Erreur de connexion',
                    message: 'Impossible de se connecter au WebSocket: ' + error,
                    timestamp: new Date().toISOString()
                }, 'error');
            });
        }

        function disconnect() {
            if (stompClient !== null) {
                stompClient.disconnect();
            }
            setConnected(false);
            console.log("Disconnected");
        }

        function sendTest() {
            const message = document.getElementById('testMessage').value;
            if (message && stompClient) {
                stompClient.send("/app/test", {}, message);
                document.getElementById('testMessage').value = '';
            }
        }

        function showNotification(notification, type = 'info') {
            const notificationsDiv = document.getElementById('notifications');
            const notificationElement = document.createElement('div');
            notificationElement.className = 'notification ' + type;
            
            const timestamp = new Date(notification.timestamp).toLocaleString();
            notificationElement.innerHTML = `
                <strong>${notification.title || 'Notification'}</strong><br>
                ${notification.message}<br>
                <small>Type: ${notification.type} | ${timestamp}</small>
                ${notification.employeeName ? `<br><small>Employé: ${notification.employeeName}</small>` : ''}
                ${notification.congeId ? `<br><small>Congé ID: ${notification.congeId}</small>` : ''}
            `;
            
            notificationsDiv.insertBefore(notificationElement, notificationsDiv.firstChild);
        }

        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                alert('Veuillez saisir nom d\'utilisateur et mot de passe');
                return;
            }
            
            fetch('/api/auth/authenticate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.token) {
                    token = data.token;
                    document.getElementById('authStatus').textContent = `Authentifié: ${data.username}`;
                    showNotification({
                        title: 'Authentification réussie',
                        message: `Connecté en tant que ${data.username}`,
                        timestamp: new Date().toISOString()
                    }, 'success');
                } else {
                    document.getElementById('authStatus').textContent = 'Échec authentification';
                    showNotification({
                        title: 'Erreur d\'authentification',
                        message: 'Nom d\'utilisateur ou mot de passe incorrect',
                        timestamp: new Date().toISOString()
                    }, 'error');
                }
            })
            .catch(error => {
                console.error('Login error:', error);
                showNotification({
                    title: 'Erreur de connexion',
                    message: 'Erreur lors de la connexion: ' + error.message,
                    timestamp: new Date().toISOString()
                }, 'error');
            });
        }

        function clearNotifications() {
            document.getElementById('notifications').innerHTML = '';
        }

        // Event listeners
        document.getElementById('connect').addEventListener('click', connect);
        document.getElementById('disconnect').addEventListener('click', disconnect);
        document.getElementById('sendTest').addEventListener('click', sendTest);
        document.getElementById('login').addEventListener('click', login);
        document.getElementById('clearNotifications').addEventListener('click', clearNotifications);
        
        // Enter key support
        document.getElementById('testMessage').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') sendTest();
        });
        
        document.getElementById('password').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') login();
        });

        // Initialize
        setConnected(false);
    </script>
</body>
</html>
