package com.tekup.rh_management.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CongeValidationRequest {
    
    @NotNull(message = "La décision est obligatoire")
    private Boolean approved;
    
    private String comment;
}
