# Script PowerShell pour tester les rôles
$baseUrl = "http://localhost:9090"

Write-Host "=== Test des Rôles et Autorisations ===" -ForegroundColor Green
Write-Host ""

# Attendre que l'application démarre
Write-Host "Attente du démarrage de l'application..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Fonction pour créer un utilisateur
function Create-User {
    param($username, $email, $role)
    
    try {
        $userData = @{
            username = $username
            email = $email
            password = "password"
            roles = @($role)
        } | ConvertTo-Json

        $response = Invoke-RestMethod -Uri "$baseUrl/api/auth/register" -Method POST -ContentType "application/json" -Body $userData
        Write-Host "✓ Utilisateur $username créé avec rôle $role" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "⚠ Utilisateur $username existe déjà ou erreur: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Fonction pour s'authentifier
function Get-AuthToken {
    param($username, $password)
    
    try {
        $loginData = @{
            username = $username
            password = $password
        } | ConvertTo-Json

        $authResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/authenticate" -Method POST -ContentType "application/json" -Body $loginData
        return $authResponse.token
    } catch {
        Write-Host "✗ Erreur authentification pour $username : $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Fonction pour tester un endpoint
function Test-Endpoint {
    param($url, $token, $description)
    
    try {
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }

        $response = Invoke-RestMethod -Uri $url -Method GET -Headers $headers
        Write-Host "✓ $description - Succès" -ForegroundColor Green
        return $response
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "✗ $description - Erreur $statusCode" -ForegroundColor Red
        return $null
    }
}

# Créer les utilisateurs
Write-Host "1. Création des utilisateurs..." -ForegroundColor Yellow
Create-User "employee1" "<EMAIL>" "EMPLOYEE"
Create-User "manager1" "<EMAIL>" "MANAGER"
Create-User "rh1" "<EMAIL>" "RH"

Write-Host ""
Write-Host "2. Test d'authentification..." -ForegroundColor Yellow

$employeeToken = Get-AuthToken "employee1" "password"
$managerToken = Get-AuthToken "manager1" "password"
$rhToken = Get-AuthToken "rh1" "password"

if ($employeeToken) { Write-Host "✓ Token employé obtenu" -ForegroundColor Green }
if ($managerToken) { Write-Host "✓ Token manager obtenu" -ForegroundColor Green }
if ($rhToken) { Write-Host "✓ Token RH obtenu" -ForegroundColor Green }

Write-Host ""
Write-Host "3. Test des informations utilisateur (debug)..." -ForegroundColor Yellow

if ($rhToken) {
    $debugInfo = Test-Endpoint "$baseUrl/api/v0/conges/debug/user-info" $rhToken "Debug info RH"
    if ($debugInfo) {
        Write-Host "  Username: $($debugInfo.username)" -ForegroundColor Cyan
        Write-Host "  Role: $($debugInfo.role)" -ForegroundColor Cyan
        Write-Host "  Authorities: $($debugInfo.authorities -join ', ')" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "4. Test des endpoints avec utilisateur RH..." -ForegroundColor Yellow

if ($rhToken) {
    Test-Endpoint "$baseUrl/api/v0/conges/pending-rh" $rhToken "Demandes en attente RH"
    Test-Endpoint "$baseUrl/api/v0/conges/all" $rhToken "Toutes les demandes"
    Test-Endpoint "$baseUrl/api/v0/conges/validated-by-me-rh" $rhToken "Demandes validées par moi (RH)"
}

Write-Host ""
Write-Host "5. Test des endpoints avec utilisateur Manager..." -ForegroundColor Yellow

if ($managerToken) {
    Test-Endpoint "$baseUrl/api/v0/conges/pending-manager" $managerToken "Demandes en attente Manager"
    Test-Endpoint "$baseUrl/api/v0/conges/validated-by-me-manager" $managerToken "Demandes validées par moi (Manager)"
}

Write-Host ""
Write-Host "6. Test des endpoints avec utilisateur Employee..." -ForegroundColor Yellow

if ($employeeToken) {
    Test-Endpoint "$baseUrl/api/v0/conges/my-conges" $employeeToken "Mes demandes de congé"
}

Write-Host ""
Write-Host "=== Test terminé ===" -ForegroundColor Green
