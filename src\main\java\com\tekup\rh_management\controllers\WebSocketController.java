package com.tekup.rh_management.controllers;

import com.tekup.rh_management.dto.NotificationMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.annotation.SendToUser;
import org.springframework.stereotype.Controller;

import java.security.Principal;
import java.time.LocalDateTime;

@Controller
@RequiredArgsConstructor
@Slf4j
public class WebSocketController {
    
    private final SimpMessagingTemplate messagingTemplate;
    
    @MessageMapping("/connect")
    @SendToUser("/queue/notifications")
    public NotificationMessage handleConnection(Principal principal) {
        log.info("Utilisateur connecté via WebSocket: {}", principal.getName());
        
        return NotificationMessage.builder()
                .type("CONNECTION")
                .title("Connexion établie")
                .message("Vous êtes maintenant connecté aux notifications en temps réel")
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    @MessageMapping("/test")
    @SendTo("/topic/notifications")
    public NotificationMessage handleTestMessage(String message, Principal principal) {
        log.info("Message de test reçu de {}: {}", principal.getName(), message);
        
        return NotificationMessage.builder()
                .type("TEST")
                .title("Message de test")
                .message("Test de notification: " + message)
                .timestamp(LocalDateTime.now())
                .build();
    }
}
