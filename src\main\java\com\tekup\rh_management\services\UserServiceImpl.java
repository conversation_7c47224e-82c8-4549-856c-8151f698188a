package com.tekup.rh_management.services;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tekup.rh_management.entities.User;
import com.tekup.rh_management.exceptions.UserNotFoundException;
import com.tekup.rh_management.repositories.UserRepository;


@Service
@Transactional
public class UserServiceImpl implements IUserService {

    private UserRepository userRepository;

    public UserServiceImpl(UserRepository userRepository) {
        this.userRepository = userRepository;
    }



    @Override
    public User updateUser(Long id, User updatedUser) throws UserNotFoundException {
        User existing = userRepository.findById(id)
                .orElseThrow(() -> new UserNotFoundException("User not found"));
        existing.setUsername(updatedUser.getUsername());
        existing.setEmail(updatedUser.getEmail());
        existing.setRole(updatedUser.getRole());
        return userRepository.save(existing);
    }

    @Override
    public void deleteUser(Long id) throws UserNotFoundException {
        if (!userRepository.existsById(id)) {
            throw new UserNotFoundException("User not found");
        }
        userRepository.deleteById(id);
    }




    @Override
    public User getUserById(Long id) throws UserNotFoundException {
        // Use eager loading to avoid LazyInitializationException when roles are accessed
        return userRepository.findByIdWithRole(id)
                .orElseThrow(() -> new UserNotFoundException("User not found"));
    }

    @Override
    public Optional<User> getUserByUsername(String username) {
        return userRepository.findByUsernameContainingIgnoreCase(username);
    }

    @Override
    public Optional<User> getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    @Override
    public List<User> getUsersByRole(String roleName) {
        return userRepository.findByRole_Name(roleName);
    }

    
}
