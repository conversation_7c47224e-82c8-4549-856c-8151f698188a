package com.tekup.rh_management.services;

import com.tekup.rh_management.dto.CongeRequest;
import com.tekup.rh_management.dto.CongeResponse;
import com.tekup.rh_management.dto.CongeValidationRequest;
import com.tekup.rh_management.entities.User;

import java.util.List;

public interface ICongesService {
    
    /**
     * Créer une nouvelle demande de congé (EMPLOYEE)
     */
    CongeResponse createCongeRequest(CongeRequest request, User employee);
    
    /**
     * Obtenir toutes les demandes de congé d'un employé
     */
    List<CongeResponse> getCongesByEmployee(User employee);
    
    /**
     * Obtenir toutes les demandes en attente de validation par le manager
     */
    List<CongeResponse> getPendingCongesForManager();
    
    /**
     * Obtenir toutes les demandes en attente de validation par les RH
     */
    List<CongeResponse> getPendingCongesForRH();
    
    /**
     * Valider une demande de congé par le manager (MANAGER)
     */
    CongeResponse validateByManager(Long congeId, CongeValidationRequest validation, User manager);
    
    /**
     * Valider une demande de congé par les RH (RH)
     */
    CongeResponse validateByRH(Long congeId, CongeValidationRequest validation, User rh);
    
    /**
     * Obtenir une demande de congé par ID
     */
    CongeResponse getCongeById(Long id);
    
    /**
     * Obtenir toutes les demandes de congé
     */
    List<CongeResponse> getAllConges();
    
    /**
     * Obtenir les demandes validées par un manager
     */
    List<CongeResponse> getCongesValidatedByManager(User manager);
    
    /**
     * Obtenir les demandes validées par un RH
     */
    List<CongeResponse> getCongesValidatedByRH(User rh);
}
