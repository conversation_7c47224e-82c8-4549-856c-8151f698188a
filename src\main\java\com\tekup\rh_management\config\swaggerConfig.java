package com.tekup.rh_management.config;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class swaggerConfig {
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info().title("Rh Managment")
                        .description(" ")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("rhManagment Team")
                                .email("<EMAIL>")
                                .url("https://yaa.rhmanagmnet.com"))
                        .license(new License()
                                .name("MIT licence")
                                .url("https://openseource.org/license/MIT")))
                .servers(List.of(
                        new Server().url("http://localhost:9090").description("development server"),
                        new Server().url("https://api.rhManagment.com").description("development server")
                ))
                .addSecurityItem(new SecurityRequirement().addList("bearer authentication"))
                .components(new Components()
                        .addSecuritySchemes("bearer authentication", createAPIKeyScheme()));
    }

    private SecurityScheme createAPIKeyScheme() {
        return new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .bearerFormat("JWT")
                .scheme("bearer");
    }}

