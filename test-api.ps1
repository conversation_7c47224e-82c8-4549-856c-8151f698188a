# Script PowerShell pour tester l'API
$baseUrl = "http://localhost:9090"

Write-Host "=== Test de l'API RH Management ===" -ForegroundColor Green
Write-Host ""

# Test de santé de l'application
Write-Host "1. Test de connexion à l'application..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/auth/register" -Method POST -ContentType "application/json" -Body '{"test": "connection"}' -ErrorAction Stop
    Write-Host "✓ Application accessible" -ForegroundColor Green
} catch {
    if ($_.Exception.Response.StatusCode -eq 400) {
        Write-Host "✓ Application accessible (erreur 400 attendue)" -ForegroundColor Green
    } else {
        Write-Host "✗ Application non accessible: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "2. Création d'un utilisateur employé..." -ForegroundColor Yellow
try {
    $employeeData = @{
        username = "employee1"
        email = "<EMAIL>"
        password = "password"
        roles = @("EMPLOYEE")
    } | ConvertTo-Json

    $response = Invoke-RestMethod -Uri "$baseUrl/api/auth/register" -Method POST -ContentType "application/json" -Body $employeeData
    Write-Host "✓ Employé créé: $($response.message)" -ForegroundColor Green
} catch {
    Write-Host "⚠ Erreur création employé: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "3. Création d'un utilisateur manager..." -ForegroundColor Yellow
try {
    $managerData = @{
        username = "manager1"
        email = "<EMAIL>"
        password = "password"
        roles = @("MANAGER")
    } | ConvertTo-Json

    $response = Invoke-RestMethod -Uri "$baseUrl/api/auth/register" -Method POST -ContentType "application/json" -Body $managerData
    Write-Host "✓ Manager créé: $($response.message)" -ForegroundColor Green
} catch {
    Write-Host "⚠ Erreur création manager: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "4. Création d'un utilisateur RH..." -ForegroundColor Yellow
try {
    $rhData = @{
        username = "rh1"
        email = "<EMAIL>"
        password = "password"
        roles = @("RH")
    } | ConvertTo-Json

    $response = Invoke-RestMethod -Uri "$baseUrl/api/auth/register" -Method POST -ContentType "application/json" -Body $rhData
    Write-Host "✓ RH créé: $($response.message)" -ForegroundColor Green
} catch {
    Write-Host "⚠ Erreur création RH: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "5. Test d'authentification employé..." -ForegroundColor Yellow
try {
    $loginData = @{
        username = "employee1"
        password = "password"
    } | ConvertTo-Json

    $authResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/authenticate" -Method POST -ContentType "application/json" -Body $loginData
    $employeeToken = $authResponse.token
    Write-Host "✓ Authentification réussie pour employee1" -ForegroundColor Green
    Write-Host "  Token: $($employeeToken.Substring(0, 50))..." -ForegroundColor Gray
} catch {
    Write-Host "✗ Erreur authentification: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "6. Test création demande de congé..." -ForegroundColor Yellow
try {
    $congeData = @{
        type = "Congés payés"
        nombreJours = 5
        dateDebut = "2024-08-01"
        dateFin = "2024-08-05"
        motif = "Vacances d'été"
    } | ConvertTo-Json

    $headers = @{
        "Authorization" = "Bearer $employeeToken"
        "Content-Type" = "application/json"
    }

    $congeResponse = Invoke-RestMethod -Uri "$baseUrl/api/v0/conges" -Method POST -Headers $headers -Body $congeData
    Write-Host "✓ Demande de congé créée avec succès" -ForegroundColor Green
    Write-Host "  ID: $($congeResponse.id)" -ForegroundColor Gray
    Write-Host "  Statut: $($congeResponse.status)" -ForegroundColor Gray
} catch {
    Write-Host "✗ Erreur création congé: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorDetails = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorDetails)
        $errorBody = $reader.ReadToEnd()
        Write-Host "  Détails: $errorBody" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "7. Test consultation des congés de l'employé..." -ForegroundColor Yellow
try {
    $myConges = Invoke-RestMethod -Uri "$baseUrl/api/v0/conges/my-conges" -Method GET -Headers $headers
    Write-Host "✓ Consultation réussie - $($myConges.Count) demande(s) trouvée(s)" -ForegroundColor Green
} catch {
    Write-Host "✗ Erreur consultation: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Test terminé ===" -ForegroundColor Green
Write-Host "Application disponible sur: $baseUrl" -ForegroundColor Cyan
Write-Host "Documentation Swagger: $baseUrl/swagger-ui.html" -ForegroundColor Cyan
