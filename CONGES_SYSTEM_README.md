# Système de Gestion des Congés - RH Management

## Vue d'ensemble

Ce système permet la gestion complète des demandes de congés avec un workflow d'approbation à deux niveaux et des notifications en temps réel via WebSocket.

## Workflow des Congés

```
EMPLOYEE crée demande → PENDING → MANAGER valide → ACCEPTED_BY_MANAGER → RH valide → APPROVED_BY_RH
                                      ↓                                        ↓
                                REJECTED_BY_MANAGER                    REJECTED_BY_RH
```

## Rôles et Permissions

### EMPLOYEE (Employé)
- ✅ Créer une demande de congé
- ✅ Consulter ses propres demandes
- ✅ Recevoir des notifications sur l'état de ses demandes

### MANAGER (Manager)
- ✅ Consulter les demandes en attente de validation
- ✅ Approuver ou rejeter les demandes (statut PENDING)
- ✅ Consulter les demandes qu'il a validées
- ✅ Recevoir des notifications pour nouvelles demandes

### RH (Ressources Humaines)
- ✅ Consulter les demandes approuvées par les managers
- ✅ Validation finale (approuver/rejeter définitivement)
- ✅ Consulter toutes les demandes du système
- ✅ Recevoir des notifications pour demandes à valider

## API Endpoints

### Authentification
- `POST /api/auth/authenticate` - Connexion
- `POST /api/auth/register` - Inscription

### Gestion des Congés
- `POST /api/v0/conges` - Créer une demande (EMPLOYEE)
- `GET /api/v0/conges/my-conges` - Mes demandes (EMPLOYEE)
- `GET /api/v0/conges/pending-manager` - Demandes à valider (MANAGER)
- `GET /api/v0/conges/pending-rh` - Demandes à valider (RH)
- `POST /api/v0/conges/{id}/validate-manager` - Validation manager (MANAGER)
- `POST /api/v0/conges/{id}/validate-rh` - Validation RH (RH)
- `GET /api/v0/conges/{id}` - Détails d'une demande
- `GET /api/v0/conges/all` - Toutes les demandes (RH)

### WebSocket
- `WS /ws` - Endpoint WebSocket pour notifications temps réel

## Statuts des Demandes

| Statut | Description | Qui peut agir |
|--------|-------------|---------------|
| `PENDING` | En attente validation manager | MANAGER |
| `ACCEPTED_BY_MANAGER` | Approuvée par manager | RH |
| `REJECTED_BY_MANAGER` | Rejetée par manager | - |
| `APPROVED_BY_RH` | Approuvée définitivement | - |
| `REJECTED_BY_RH` | Rejetée par RH | - |

## Notifications WebSocket

### Types de notifications
- `CONGE_CREATED` - Nouvelle demande créée
- `CONGE_VALIDATED_BY_MANAGER` - Validation par manager
- `CONGE_REJECTED_BY_MANAGER` - Rejet par manager
- `CONGE_APPROVED_BY_RH` - Approbation finale RH
- `CONGE_REJECTED_BY_RH` - Rejet par RH

### Destinataires des notifications
- **Employé** : Toutes les notifications concernant ses demandes
- **Managers** : Nouvelles demandes à valider
- **RH** : Demandes approuvées par managers à valider

## Configuration

### Base de données
```properties
spring.datasource.url=************************************************************
spring.datasource.username=root
spring.datasource.password=
```

### WebSocket
- Endpoint: `/ws`
- Protocole: STOMP over SockJS
- Destinations:
  - `/user/queue/notifications` - Notifications personnelles
  - `/topic/notifications` - Notifications générales

## Test du système

### 1. Créer les utilisateurs
```bash
# Créer un employé
curl -X POST http://localhost:9090/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"employee1","email":"<EMAIL>","password":"password","roles":["EMPLOYEE"]}'

# Créer un manager
curl -X POST http://localhost:9090/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"manager1","email":"<EMAIL>","password":"password","roles":["MANAGER"]}'

# Créer un RH
curl -X POST http://localhost:9090/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"rh1","email":"<EMAIL>","password":"password","roles":["RH"]}'
```

### 2. Tester le workflow
1. **Employé** crée une demande
2. **Manager** reçoit notification et valide
3. **RH** reçoit notification et valide définitivement
4. **Employé** reçoit notification finale

### 3. Test WebSocket
Ouvrir `http://localhost:9090/websocket-test.html` pour tester les notifications en temps réel.

## Sécurité

- Authentification JWT
- Autorisation basée sur les rôles (@PreAuthorize)
- Validation des données d'entrée
- Gestion des exceptions globale

## Fonctionnalités avancées

### Validation des conflits
- Vérification automatique des chevauchements de dates
- Empêche les demandes conflictuelles

### Audit trail
- Horodatage de toutes les actions
- Commentaires des validateurs
- Historique complet des changements d'état

### Notifications intelligentes
- Notifications ciblées selon le rôle
- Messages contextuels
- Support temps réel via WebSocket

## Démarrage rapide

1. Cloner le projet
2. Configurer la base de données MySQL
3. Lancer l'application: `mvn spring-boot:run`
4. Accéder à Swagger: `http://localhost:9090/swagger-ui.html`
5. Tester WebSocket: `http://localhost:9090/websocket-test.html`

## Technologies utilisées

- Spring Boot 3.2.0
- Spring Security
- Spring Data JPA
- WebSocket (STOMP)
- MySQL
- JWT
- Swagger/OpenAPI
- Lombok
