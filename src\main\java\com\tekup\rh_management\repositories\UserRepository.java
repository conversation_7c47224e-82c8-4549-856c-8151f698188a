package com.tekup.rh_management.repositories;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tekup.rh_management.entities.User;


@Repository public interface UserRepository extends JpaRepository<User, Long> {

    Optional<User> findByUsername(String username);
    Optional<User> findByUsernameContainingIgnoreCase(String username);
    Optional<User> findByEmail(String email);
    List<User> findByRole_Name(String roleName);

    // Fetch user with role eagerly to avoid LazyInitializationException
    @org.springframework.data.jpa.repository.Query("SELECT u FROM User u LEFT JOIN FETCH u.role WHERE u.username = :username")
    Optional<User> findByUsernameWithRole(String username);

    // Fetch all users with role eagerly to avoid LazyInitializationException
    @org.springframework.data.jpa.repository.Query("SELECT u FROM User u LEFT JOIN FETCH u.role")
    List<User> findAllWithRole();

    // Fetch user by ID with role eagerly to avoid LazyInitializationException
    @org.springframework.data.jpa.repository.Query("SELECT u FROM User u LEFT JOIN FETCH u.role WHERE u.id = :id")
    Optional<User> findByIdWithRole(Long id);
}
