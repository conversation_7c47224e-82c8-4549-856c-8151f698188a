#!/bin/bash

# Script de test pour le système de congés
BASE_URL="http://localhost:9090"

echo "=== Test du Système de Congés RH ==="
echo ""

# Fonction pour faire une requête POST
post_request() {
    local url=$1
    local data=$2
    local token=$3
    
    if [ -n "$token" ]; then
        curl -s -X POST "$url" \
             -H "Content-Type: application/json" \
             -H "Authorization: Bearer $token" \
             -d "$data"
    else
        curl -s -X POST "$url" \
             -H "Content-Type: application/json" \
             -d "$data"
    fi
}

# Fonction pour faire une requête GET
get_request() {
    local url=$1
    local token=$2
    
    if [ -n "$token" ]; then
        curl -s -X GET "$url" \
             -H "Authorization: Bearer $token"
    else
        curl -s -X GET "$url"
    fi
}

echo "1. Création des utilisateurs de test..."

# Créer un employé
echo "Création de l'employé..."
EMPLOYEE_RESPONSE=$(post_request "$BASE_URL/api/auth/register" '{
    "username": "employee1",
    "email": "<EMAIL>",
    "password": "password",
    "roles": ["EMPLOYEE"]
}')
echo "Réponse employé: $EMPLOYEE_RESPONSE"

# Créer un manager
echo "Création du manager..."
MANAGER_RESPONSE=$(post_request "$BASE_URL/api/auth/register" '{
    "username": "manager1",
    "email": "<EMAIL>",
    "password": "password",
    "roles": ["MANAGER"]
}')
echo "Réponse manager: $MANAGER_RESPONSE"

# Créer un RH
echo "Création du RH..."
RH_RESPONSE=$(post_request "$BASE_URL/api/auth/register" '{
    "username": "rh1",
    "email": "<EMAIL>",
    "password": "password",
    "roles": ["RH"]
}')
echo "Réponse RH: $RH_RESPONSE"

echo ""
echo "2. Authentification des utilisateurs..."

# Authentifier l'employé
EMPLOYEE_AUTH=$(post_request "$BASE_URL/api/auth/authenticate" '{
    "username": "employee1",
    "password": "password"
}')
EMPLOYEE_TOKEN=$(echo $EMPLOYEE_AUTH | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo "Token employé: ${EMPLOYEE_TOKEN:0:50}..."

# Authentifier le manager
MANAGER_AUTH=$(post_request "$BASE_URL/api/auth/authenticate" '{
    "username": "manager1",
    "password": "password"
}')
MANAGER_TOKEN=$(echo $MANAGER_AUTH | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo "Token manager: ${MANAGER_TOKEN:0:50}..."

# Authentifier le RH
RH_AUTH=$(post_request "$BASE_URL/api/auth/authenticate" '{
    "username": "rh1",
    "password": "password"
}')
RH_TOKEN=$(echo $RH_AUTH | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo "Token RH: ${RH_TOKEN:0:50}..."

echo ""
echo "3. Test du workflow de congés..."

# L'employé crée une demande de congé
echo "Création d'une demande de congé par l'employé..."
CONGE_RESPONSE=$(post_request "$BASE_URL/api/v0/conges" '{
    "type": "Congés payés",
    "nombreJours": 5,
    "dateDebut": "2024-08-01",
    "dateFin": "2024-08-05",
    "motif": "Vacances d'\''été"
}' "$EMPLOYEE_TOKEN")
echo "Réponse création congé: $CONGE_RESPONSE"

# Extraire l'ID du congé
CONGE_ID=$(echo $CONGE_RESPONSE | grep -o '"id":[0-9]*' | cut -d':' -f2)
echo "ID du congé créé: $CONGE_ID"

# Le manager consulte les demandes en attente
echo ""
echo "Consultation des demandes en attente par le manager..."
PENDING_MANAGER=$(get_request "$BASE_URL/api/v0/conges/pending-manager" "$MANAGER_TOKEN")
echo "Demandes en attente manager: $PENDING_MANAGER"

# Le manager valide la demande
if [ -n "$CONGE_ID" ]; then
    echo ""
    echo "Validation de la demande par le manager..."
    MANAGER_VALIDATION=$(post_request "$BASE_URL/api/v0/conges/$CONGE_ID/validate-manager" '{
        "approved": true,
        "comment": "Demande approuvée par le manager"
    }' "$MANAGER_TOKEN")
    echo "Réponse validation manager: $MANAGER_VALIDATION"
    
    # Le RH consulte les demandes en attente
    echo ""
    echo "Consultation des demandes en attente par le RH..."
    PENDING_RH=$(get_request "$BASE_URL/api/v0/conges/pending-rh" "$RH_TOKEN")
    echo "Demandes en attente RH: $PENDING_RH"
    
    # Le RH valide définitivement
    echo ""
    echo "Validation finale par le RH..."
    RH_VALIDATION=$(post_request "$BASE_URL/api/v0/conges/$CONGE_ID/validate-rh" '{
        "approved": true,
        "comment": "Demande approuvée définitivement par les RH"
    }' "$RH_TOKEN")
    echo "Réponse validation RH: $RH_VALIDATION"
fi

# L'employé consulte ses demandes
echo ""
echo "Consultation des demandes de l'employé..."
MY_CONGES=$(get_request "$BASE_URL/api/v0/conges/my-conges" "$EMPLOYEE_TOKEN")
echo "Mes congés: $MY_CONGES"

echo ""
echo "=== Test terminé ==="
echo ""
echo "Pour tester les WebSockets, ouvrez: $BASE_URL/websocket-test.html"
echo "Documentation API: $BASE_URL/swagger-ui.html"
