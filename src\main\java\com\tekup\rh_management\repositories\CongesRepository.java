package com.tekup.rh_management.repositories;

import com.tekup.rh_management.entities.Conges;
import com.tekup.rh_management.entities.User;
import com.tekup.rh_management.enums.CongeStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface CongesRepository extends JpaRepository<Conges, Long> {
    
    /**
     * Trouve toutes les demandes de congé d'un employé
     */
    List<Conges> findByEmployeeOrderByCreatedAtDesc(User employee);
    
    /**
     * Trouve toutes les demandes de congé d'un employé par ID
     */
    List<Conges> findByEmployee_IdOrderByCreatedAtDesc(Long employeeId);
    
    /**
     * Trouve toutes les demandes de congé par statut
     */
    List<Conges> findByStatusOrderByCreatedAtDesc(CongeStatus status);
    
    /**
     * Trouve toutes les demandes en attente de validation par le manager
     */
    @Query("SELECT c FROM Conges c WHERE c.status = 'PENDING' ORDER BY c.createdAt ASC")
    List<Conges> findPendingForManagerValidation();
    
    /**
     * Trouve toutes les demandes acceptées par le manager et en attente de validation RH
     */
    @Query("SELECT c FROM Conges c WHERE c.status = 'ACCEPTED_BY_MANAGER' ORDER BY c.managerValidatedAt ASC")
    List<Conges> findPendingForRHValidation();
    
    /**
     * Trouve les demandes de congé d'un employé avec un statut spécifique
     */
    List<Conges> findByEmployeeAndStatusOrderByCreatedAtDesc(User employee, CongeStatus status);
    
    /**
     * Trouve les demandes de congé validées par un manager spécifique
     */
    List<Conges> findByManagerOrderByManagerValidatedAtDesc(User manager);
    
    /**
     * Trouve les demandes de congé validées par un RH spécifique
     */
    List<Conges> findByRhOrderByRhValidatedAtDesc(User rh);
    
    /**
     * Vérifie s'il y a des conflits de dates pour un employé
     */
    @Query("SELECT c FROM Conges c WHERE c.employee = :employee " +
           "AND c.status IN ('ACCEPTED_BY_MANAGER', 'APPROVED_BY_RH') " +
           "AND ((c.dateDebut <= :dateFin AND c.dateFin >= :dateDebut))")
    List<Conges> findConflictingConges(@Param("employee") User employee, 
                                      @Param("dateDebut") LocalDate dateDebut, 
                                      @Param("dateFin") LocalDate dateFin);
    
    /**
     * Trouve une demande de congé avec tous ses détails (employee, manager, rh)
     */
    @Query("SELECT c FROM Conges c " +
           "LEFT JOIN FETCH c.employee " +
           "LEFT JOIN FETCH c.manager " +
           "LEFT JOIN FETCH c.rh " +
           "WHERE c.id = :id")
    Optional<Conges> findByIdWithDetails(@Param("id") Long id);
    
    /**
     * Compte le nombre de jours de congé approuvés pour un employé dans une année
     */
    @Query("SELECT COALESCE(SUM(c.nombreJours), 0) FROM Conges c " +
           "WHERE c.employee = :employee " +
           "AND c.status = 'APPROVED_BY_RH' " +
           "AND YEAR(c.dateDebut) = :year")
    int countApprovedDaysForEmployeeInYear(@Param("employee") User employee, @Param("year") int year);
    
    /**
     * Trouve toutes les demandes de congé dans une période donnée
     */
    @Query("SELECT c FROM Conges c WHERE c.dateDebut <= :dateFin AND c.dateFin >= :dateDebut " +
           "ORDER BY c.dateDebut ASC")
    List<Conges> findCongesInPeriod(@Param("dateDebut") LocalDate dateDebut, 
                                   @Param("dateFin") LocalDate dateFin);
}
